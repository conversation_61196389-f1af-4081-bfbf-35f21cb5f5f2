@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;

    /* Sidebar specific variables */
    --sidebar: 0 0% 100%;
    --sidebar-foreground: 240 10% 3.9%;
    --sidebar-muted: 240 4.8% 95.9%;
    --sidebar-muted-foreground: 240 3.8% 46.1%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 240 5.9% 90%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;

    /* Sidebar specific variables */
    --sidebar: 240 10% 3.9%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-muted: 240 3.7% 15.9%;
    --sidebar-muted-foreground: 240 5% 64.9%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 240 3.7% 15.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
  }
  
  /* Enhanced smooth transitions for theme changes */
  *,
  *::before,
  *::after {
    transition: 
      background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      border-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      fill 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      stroke 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Specific transitions for common UI elements */
  [data-radix-collection-item],
  [role="menuitem"],
  [role="button"],
  button,
  input,
  select,
  textarea {
    transition: 
      background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Ensure cards and containers transition smoothly */
  [class*="card"],
  [class*="dialog"],
  [class*="popover"],
  [class*="dropdown"],
  [class*="sheet"] {
    transition: 
      background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      border-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Sidebar styles with smooth transitions */
.bg-sidebar {
  background-color: hsl(var(--sidebar));
  transition: background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.text-sidebar-foreground {
  color: hsl(var(--sidebar-foreground));
  transition: color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.bg-sidebar-accent {
  background-color: hsl(var(--sidebar-accent));
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.text-sidebar-accent-foreground {
  color: hsl(var(--sidebar-accent-foreground));
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Additional theme transition styles */
svg {
  transition: 
    fill 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    stroke 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Chart and graph elements */
.recharts-wrapper,
[class*="chart"] {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth transitions for any backdrop or overlay */
[data-state],
[data-side] {
  transition: 
    background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Add responsive utility classes */
@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* 3D Carousel Perspective and Transform Styles */
.carousel-container {
  perspective: 1500px;
  perspective-origin: center center;
}

.carousel-track {
  transform-style: preserve-3d;
  position: relative;
}

.game-card {
  transform-style: preserve-3d;
  backface-visibility: hidden;
  will-change: transform, opacity;
}

/* Enhanced 3D positioning classes */
.card-center {
  transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1) !important;
  z-index: 30;
  opacity: 1 !important;
}

.card-left {
  transform: translateX(-320px) translateZ(0px) rotateY(-15deg) scale(0.9) !important;
  z-index: 20;
  opacity: 1 !important;
}

.card-right {
  transform: translateX(320px) translateZ(0px) rotateY(15deg) scale(0.9) !important;
  z-index: 20;
  opacity: 1 !important;
}

.card-far-left {
  transform: translateX(-640px) translateZ(-100px) rotateY(-25deg) scale(0.7) !important;
  z-index: 10;
  opacity: 0.4 !important;
}

.card-far-right {
  transform: translateX(640px) translateZ(-100px) rotateY(25deg) scale(0.7) !important;
  z-index: 10;
  opacity: 0.4 !important;
}

/* Rolling animation states */
.card-rolling-left {
  animation: roll-to-left 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.card-rolling-right {
  animation: roll-to-right 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.card-entering-left {
  animation: enter-from-left 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.card-entering-right {
  animation: enter-from-right 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.card-exiting-left {
  animation: exit-to-left 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.card-exiting-right {
  animation: exit-to-right 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes card-slide-in {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.card-enter {
  animation: card-slide-in 0.6s ease-out forwards;
}

@keyframes shine {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes shine-wave {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-shine {
  background-size: 200% auto;
  animation: shine 3s linear infinite;
}

.animate-shine-wave {
  animation: shine-wave 3s ease-in-out infinite;
}

/* 3D Rolling Keyframe Animations */
@keyframes roll-to-left {
  0% {
    transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1);
    opacity: 1;
  }
  20% {
    transform: translateX(-64px) translateZ(80px) rotateY(-30deg) scale(1.05);
    opacity: 0.9;
  }
  40% {
    transform: translateX(-128px) translateZ(40px) rotateY(-60deg) scale(1);
    opacity: 0.7;
  }
  60% {
    transform: translateX(-192px) translateZ(20px) rotateY(-90deg) scale(0.95);
    opacity: 0.8;
  }
  80% {
    transform: translateX(-256px) translateZ(10px) rotateY(-120deg) scale(0.92);
    opacity: 0.9;
  }
  100% {
    transform: translateX(-320px) translateZ(0px) rotateY(-15deg) scale(0.9);
    opacity: 1;
  }
}

@keyframes roll-to-right {
  0% {
    transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1);
    opacity: 1;
  }
  20% {
    transform: translateX(64px) translateZ(80px) rotateY(30deg) scale(1.05);
    opacity: 0.9;
  }
  40% {
    transform: translateX(128px) translateZ(40px) rotateY(60deg) scale(1);
    opacity: 0.7;
  }
  60% {
    transform: translateX(192px) translateZ(20px) rotateY(90deg) scale(0.95);
    opacity: 0.8;
  }
  80% {
    transform: translateX(256px) translateZ(10px) rotateY(120deg) scale(0.92);
    opacity: 0.9;
  }
  100% {
    transform: translateX(320px) translateZ(0px) rotateY(15deg) scale(0.9);
    opacity: 1;
  }
}

@keyframes enter-from-left {
  0% {
    transform: translateX(-640px) translateZ(-100px) rotateY(-25deg) scale(0.7);
    opacity: 0.4;
  }
  25% {
    transform: translateX(-480px) translateZ(-50px) rotateY(-135deg) scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: translateX(-320px) translateZ(0px) rotateY(-90deg) scale(0.9);
    opacity: 0.8;
  }
  75% {
    transform: translateX(-160px) translateZ(50px) rotateY(-45deg) scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1);
    opacity: 1;
  }
}

@keyframes enter-from-right {
  0% {
    transform: translateX(640px) translateZ(-100px) rotateY(25deg) scale(0.7);
    opacity: 0.4;
  }
  25% {
    transform: translateX(480px) translateZ(-50px) rotateY(135deg) scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: translateX(320px) translateZ(0px) rotateY(90deg) scale(0.9);
    opacity: 0.8;
  }
  75% {
    transform: translateX(160px) translateZ(50px) rotateY(45deg) scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1);
    opacity: 1;
  }
}

@keyframes exit-to-left {
  0% {
    transform: translateX(-320px) translateZ(0px) rotateY(-15deg) scale(0.9);
    opacity: 1;
  }
  25% {
    transform: translateX(-400px) translateZ(-25px) rotateY(-60deg) scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: translateX(-480px) translateZ(-50px) rotateY(-105deg) scale(0.75);
    opacity: 0.6;
  }
  75% {
    transform: translateX(-560px) translateZ(-75px) rotateY(-150deg) scale(0.7);
    opacity: 0.5;
  }
  100% {
    transform: translateX(-640px) translateZ(-100px) rotateY(-25deg) scale(0.7);
    opacity: 0.4;
  }
}

@keyframes exit-to-right {
  0% {
    transform: translateX(320px) translateZ(0px) rotateY(15deg) scale(0.9);
    opacity: 1;
  }
  25% {
    transform: translateX(400px) translateZ(-25px) rotateY(60deg) scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: translateX(480px) translateZ(-50px) rotateY(105deg) scale(0.75);
    opacity: 0.6;
  }
  75% {
    transform: translateX(560px) translateZ(-75px) rotateY(150deg) scale(0.7);
    opacity: 0.5;
  }
  100% {
    transform: translateX(640px) translateZ(-100px) rotateY(25deg) scale(0.7);
    opacity: 0.4;
  }
}
